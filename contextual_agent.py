#!/usr/bin/env python3
"""
Contextual Agent-S - Intelligent Context-Aware Automation
Analyzes prompts, creates plans, uses system context and resources
"""

import os
import time
import json
import logging
import base64
import io
from pathlib import Path
import pyautogui
from PIL import Image

# AI imports
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

# Configure
pyautogui.PAUSE = 0.3
pyautogui.FAILSAFE = True
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContextualAgent:
    """Intelligent agent with context awareness and planning"""
    
    def __init__(self):
        logger.info("🧠 Initializing Contextual Agent...")
        
        # AI Setup
        self.ai_client = None
        if HAS_OPENAI and os.getenv('OPENAI_API_KEY'):
            try:
                self.ai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
                logger.info("✅ AI Vision enabled")
            except Exception as e:
                logger.warning(f"AI setup failed: {e}")
        
        # System context
        self.system_context = {}
        self.available_resources = []
        self.current_plan = None
        
        # Initialize system understanding
        self._gather_system_context()
        
        logger.info("✅ Contextual Agent ready!")
    
    def _gather_system_context(self):
        """Gather information about user's system and resources"""
        logger.info("🔍 Gathering system context...")
        
        # Get current directory and files
        current_dir = Path.cwd()
        self.system_context = {
            "current_directory": str(current_dir),
            "available_files": [],
            "open_applications": [],
            "screen_resolution": pyautogui.size(),
            "timestamp": time.time()
        }
        
        # Scan for relevant files
        try:
            for file_path in current_dir.glob("*"):
                if file_path.is_file() and file_path.suffix in ['.txt', '.csv', '.json', '.py', '.md']:
                    file_info = {
                        "name": file_path.name,
                        "path": str(file_path),
                        "size": file_path.stat().st_size,
                        "modified": file_path.stat().st_mtime
                    }
                    
                    # Read content preview for small files
                    if file_path.stat().st_size < 10000:  # Less than 10KB
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()[:500]  # First 500 chars
                                file_info["preview"] = content
                        except:
                            file_info["preview"] = "Could not read content"
                    
                    self.available_resources.append(file_info)
            
            logger.info(f"📁 Found {len(self.available_resources)} relevant files")
            
        except Exception as e:
            logger.warning(f"File scanning failed: {e}")
    
    def analyze_and_execute(self, user_prompt):
        """Main method: analyze prompt, create plan, execute with context"""
        start_time = time.time()
        
        logger.info(f"🎯 CONTEXTUAL EXECUTION: {user_prompt}")
        
        # Step 1: Brief Analysis
        analysis = self._analyze_prompt(user_prompt)
        print(f"\n📋 ANALYSIS:")
        print(f"   Intent: {analysis['intent']}")
        print(f"   Complexity: {analysis['complexity']}")
        print(f"   Resources needed: {', '.join(analysis['resources_needed'])}")
        
        # Step 2: Gather Current Context
        current_context = self._get_current_context()
        print(f"\n🔍 CURRENT CONTEXT:")
        print(f"   Screen state: {current_context['screen_state']}")
        print(f"   Available files: {len(current_context['available_files'])}")
        if current_context['relevant_files']:
            print(f"   Relevant files: {', '.join([f['name'] for f in current_context['relevant_files']])}")
        
        # Step 3: Create Step-by-Step Plan
        plan = self._create_logical_plan(user_prompt, analysis, current_context)
        print(f"\n📝 EXECUTION PLAN:")
        for i, step in enumerate(plan['steps'], 1):
            print(f"   {i}. {step['action']} - {step['description']}")
        
        # Step 4: Execute Plan
        print(f"\n⚡ EXECUTING PLAN...")
        results = self._execute_plan(plan, current_context)
        
        # Step 5: Report Results
        execution_time = time.time() - start_time
        success = all(r.get('success', False) for r in results)
        
        print(f"\n📊 RESULTS:")
        print(f"   Success: {'✅' if success else '❌'}")
        print(f"   Time: {execution_time:.1f}s")
        print(f"   Steps completed: {len([r for r in results if r.get('success')])}/{len(results)}")
        
        return {
            "success": success,
            "execution_time": execution_time,
            "analysis": analysis,
            "plan": plan,
            "results": results,
            "context_used": current_context
        }
    
    def _analyze_prompt(self, prompt):
        """Analyze user prompt to understand intent and requirements"""
        prompt_lower = prompt.lower()
        
        # Determine intent
        intent = "unknown"
        if any(word in prompt_lower for word in ["open", "launch", "start"]):
            intent = "application_control"
        elif any(word in prompt_lower for word in ["search", "find", "look"]):
            intent = "information_retrieval"
        elif any(word in prompt_lower for word in ["file", "document", "report"]):
            intent = "file_operation"
        elif any(word in prompt_lower for word in ["screenshot", "capture", "image"]):
            intent = "screen_capture"
        elif any(word in prompt_lower for word in ["analyze", "examine", "review"]):
            intent = "data_analysis"
        
        # Determine complexity
        complexity = "simple"
        if len(prompt.split()) > 10:
            complexity = "complex"
        elif any(word in prompt_lower for word in ["and", "then", "after", "also"]):
            complexity = "multi-step"
        
        # Identify needed resources
        resources_needed = []
        if any(word in prompt_lower for word in ["browser", "web", "internet"]):
            resources_needed.append("browser")
        if any(word in prompt_lower for word in ["file", "document", "report"]):
            resources_needed.append("file_system")
        if any(word in prompt_lower for word in ["screen", "display", "window"]):
            resources_needed.append("screen_control")
        
        return {
            "intent": intent,
            "complexity": complexity,
            "resources_needed": resources_needed,
            "keywords": [word for word in prompt_lower.split() if len(word) > 3]
        }
    
    def _get_current_context(self):
        """Get current system context including screen state and files"""
        context = {
            "screen_state": "unknown",
            "available_files": self.available_resources,
            "relevant_files": [],
            "screen_analysis": None
        }
        
        # Analyze current screen if AI available
        if self.ai_client:
            try:
                screenshot = pyautogui.screenshot()
                screen_analysis = self._analyze_screen_with_ai(screenshot)
                context["screen_analysis"] = screen_analysis
                context["screen_state"] = screen_analysis.get("state", "unknown")
            except Exception as e:
                logger.warning(f"Screen analysis failed: {e}")
                context["screen_state"] = "analysis_failed"
        else:
            context["screen_state"] = "no_ai_available"
        
        # Find relevant files based on recent activity
        try:
            # Look for recently modified files
            recent_files = [f for f in self.available_resources 
                          if time.time() - f.get("modified", 0) < 3600]  # Last hour
            context["relevant_files"] = recent_files[:3]  # Top 3 recent files
        except:
            pass
        
        return context
    
    def _analyze_screen_with_ai(self, screenshot):
        """Use AI to analyze current screen state"""
        try:
            # Convert screenshot to base64
            buffer = io.BytesIO()
            screenshot.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            prompt = """Analyze this screen and provide context information.

Respond with JSON:
{
    "state": "desktop/browser_open/file_editor/application_running",
    "applications_visible": ["app1", "app2"],
    "main_content": "description of what's on screen",
    "actionable_elements": ["element1", "element2"],
    "current_focus": "what window/app has focus"
}

Be concise and focus on actionable information."""
            
            response = self.ai_client.chat.completions.create(
                model="gpt-4o",
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}", "detail": "low"}}
                    ]
                }],
                max_tokens=300,
                temperature=0.1
            )
            
            analysis_text = response.choices[0].message.content
            
            # Try to parse JSON
            try:
                import re
                json_match = re.search(r'\{.*\}', analysis_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
            except:
                pass
            
            # Fallback to text analysis
            return {
                "state": "analyzed",
                "main_content": analysis_text[:200],
                "applications_visible": [],
                "actionable_elements": [],
                "current_focus": "unknown"
            }
            
        except Exception as e:
            logger.error(f"AI screen analysis failed: {e}")
            return {"state": "analysis_error", "error": str(e)}
    
    def _create_logical_plan(self, prompt, analysis, context):
        """Create a logical step-by-step plan based on context"""
        plan = {
            "goal": prompt,
            "strategy": "context_aware",
            "steps": []
        }
        
        # Plan based on intent and context
        if analysis["intent"] == "file_operation":
            # Check if specific file mentioned or use context
            if "report" in prompt.lower() and context["relevant_files"]:
                report_file = next((f for f in context["relevant_files"] if "report" in f["name"].lower()), None)
                if report_file:
                    plan["steps"].extend([
                        {"action": "locate_file", "description": f"Locate {report_file['name']}", "target": report_file},
                        {"action": "open_file", "description": f"Open {report_file['name']}", "target": report_file},
                        {"action": "analyze_content", "description": "Analyze file content", "target": report_file}
                    ])
        
        elif analysis["intent"] == "information_retrieval":
            if "browser" in analysis["resources_needed"]:
                plan["steps"].extend([
                    {"action": "open_browser", "description": "Open web browser"},
                    {"action": "search", "description": f"Search for information", "query": self._extract_search_query(prompt)},
                    {"action": "analyze_results", "description": "Analyze search results"}
                ])
        
        elif analysis["intent"] == "application_control":
            app_name = self._extract_app_name(prompt)
            plan["steps"].extend([
                {"action": "launch_app", "description": f"Launch {app_name}", "target": app_name},
                {"action": "verify_launch", "description": f"Verify {app_name} opened correctly"}
            ])
        
        elif analysis["intent"] == "screen_capture":
            plan["steps"].extend([
                {"action": "take_screenshot", "description": "Capture current screen"},
                {"action": "save_screenshot", "description": "Save screenshot with timestamp"}
            ])
        
        # Add context-aware steps
        if context["screen_state"] == "file_editor" and analysis["intent"] != "file_operation":
            plan["steps"].insert(0, {"action": "minimize_editor", "description": "Minimize current file editor"})
        
        # Default fallback plan
        if not plan["steps"]:
            plan["steps"] = [
                {"action": "analyze_request", "description": "Analyze user request in detail"},
                {"action": "ask_clarification", "description": "Ask user for clarification"}
            ]
        
        return plan
    
    def _execute_plan(self, plan, context):
        """Execute the planned steps"""
        results = []
        
        for i, step in enumerate(plan["steps"]):
            logger.info(f"📋 Step {i+1}: {step['action']} - {step['description']}")
            
            try:
                if step["action"] == "open_browser":
                    result = self._open_browser()
                elif step["action"] == "search":
                    result = self._search_web(step.get("query", ""))
                elif step["action"] == "take_screenshot":
                    result = self._take_screenshot()
                elif step["action"] == "locate_file":
                    result = self._locate_file(step.get("target"))
                elif step["action"] == "open_file":
                    result = self._open_file(step.get("target"))
                elif step["action"] == "launch_app":
                    result = self._launch_app(step.get("target"))
                elif step["action"] == "ask_clarification":
                    result = self._ask_clarification(plan["goal"])
                else:
                    result = {"success": True, "action": step["action"], "message": "Step completed"}
                
                results.append(result)
                
                if result.get("success"):
                    print(f"   ✅ {step['description']}")
                else:
                    print(f"   ❌ {step['description']} - {result.get('error', 'Failed')}")
                    
                time.sleep(0.5)  # Brief pause between steps
                
            except Exception as e:
                error_result = {"success": False, "action": step["action"], "error": str(e)}
                results.append(error_result)
                print(f"   ❌ {step['description']} - Error: {e}")
        
        return results
    
    def _extract_search_query(self, prompt):
        """Extract search query from prompt"""
        prompt_lower = prompt.lower()
        if "bitcoin" in prompt_lower or "crypto" in prompt_lower:
            return "bitcoin price cryptocurrency"
        elif "weather" in prompt_lower:
            return "weather forecast today"
        else:
            # Extract words after search-related terms
            words = prompt.split()
            search_terms = ["search", "find", "look", "about"]
            for i, word in enumerate(words):
                if word.lower() in search_terms and i + 1 < len(words):
                    return " ".join(words[i+1:])
            return "general information"
    
    def _extract_app_name(self, prompt):
        """Extract application name from prompt"""
        prompt_lower = prompt.lower()
        if "browser" in prompt_lower:
            return "chrome"
        elif "calculator" in prompt_lower:
            return "calculator"
        elif "notepad" in prompt_lower:
            return "notepad"
        else:
            words = prompt.split()
            for word in words:
                if word.lower() in ["chrome", "firefox", "edge", "calculator", "notepad", "excel", "word"]:
                    return word.lower()
            return "unknown_app"
    
    def _open_browser(self):
        """Open web browser"""
        try:
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write('chrome')
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(3)
            return {"success": True, "action": "browser_opened"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _search_web(self, query):
        """Search the web"""
        try:
            pyautogui.hotkey('ctrl', 'l')
            time.sleep(1)
            pyautogui.write(query)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            return {"success": True, "action": "search_completed", "query": query}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _take_screenshot(self):
        """Take screenshot"""
        try:
            screenshot = pyautogui.screenshot()
            filename = f"contextual_screenshot_{int(time.time())}.png"
            screenshot.save(filename)
            return {"success": True, "action": "screenshot_taken", "filename": filename}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _locate_file(self, file_info):
        """Locate a specific file"""
        if file_info and "path" in file_info:
            return {"success": True, "action": "file_located", "path": file_info["path"]}
        return {"success": False, "error": "File not found"}
    
    def _open_file(self, file_info):
        """Open a specific file"""
        try:
            if file_info and "path" in file_info:
                os.startfile(file_info["path"])
                return {"success": True, "action": "file_opened", "path": file_info["path"]}
            return {"success": False, "error": "No file specified"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _launch_app(self, app_name):
        """Launch an application"""
        try:
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write(app_name)
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(2)
            return {"success": True, "action": "app_launched", "app": app_name}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _ask_clarification(self, original_request):
        """Ask user for clarification"""
        print(f"\n❓ I need clarification for: '{original_request}'")
        print("Available resources:")
        for resource in self.available_resources[:5]:  # Show top 5
            print(f"   📄 {resource['name']}")
        
        clarification = input("\n🤔 What specifically would you like me to do? ")
        return {"success": True, "action": "clarification_received", "clarification": clarification}

def main():
    """Main function"""
    print("🧠 CONTEXTUAL AGENT-S")
    print("=" * 40)
    print("Intelligent context-aware automation")
    print("Analyzes prompts, creates plans, uses your resources")
    print()
    
    try:
        agent = ContextualAgent()
        
        print("\n💬 Ready for intelligent automation!")
        print("I will analyze your requests, understand your context,")
        print("and create logical plans to accomplish your goals.")
        print("\nExamples:")
        print("• 'analyze the report file'")
        print("• 'search for bitcoin information'") 
        print("• 'open calculator and take screenshot'")
        print("\nType 'quit' to exit")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n🧠 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    print("👋 Goodbye!")
                    break
                
                if not user_input:
                    continue
                
                # Execute with full context analysis
                result = agent.analyze_and_execute(user_input)
                
                if not result['success']:
                    print(f"\n⚠️ Some steps failed. Check the execution log above.")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Failed to start: {e}")

if __name__ == "__main__":
    main()
